<template>
    <div class="box" v-loading="loading">
        <h4 class="box_title">
            <img src="@assets/back.png" class="box_title_back" @click="goBack" />
            {{ t('center.subtitle') }}
            <div class="box_title_action" v-if="orderDetail.id">
                <div v-if="orderDetail.status === 'unpaid'" class="box_title_action_item active" @click="payOrder">
                    {{ t('center.toPay') }}
                </div>
                <div v-if="orderDetail.status === 'unpaid'" class="box_title_action_item" @click="cancelOrder">
                    {{ t('center.cancel') }}
                </div>
                <div v-if="orderDetail.status === 'shipped'" class="box_title_action_item active" @click="confirmOrder">
                    确认收货
                </div>
                <div v-if="orderDetail.status === 'completed'" class="apply_aftersale_btn" @click="applyAftersale()">
                    申请售后
                </div>
            </div>
        </h4>

        <div v-if="orderDetail.id">
            <!-- 产品信息卡片 -->
            <div class="product_card" v-for="product in orderDetail.order_products" :key="product.id">
                <div class="product_card_image">
                    <img :src="getProductImage(product)" :alt="product.name" />
                </div>
                <div class="product_card_content">
                    <div class="product_card_header">
                        <h3 class="product_name">{{ product.name || 'Ledger Flex' }}</h3>
                        <div class="product_card_actions">
                            <div class="product_status">{{ orderDetail.status_format ||
                                getOrderStatusText(orderDetail.status) }}</div>

                        </div>
                    </div>
                    <div class="product_quantity">x {{ product.quantity || 1 }}</div>
                    <div class="product_price">{{ product.price_format || orderDetail.total_format }}</div>

                    <div class="order_details">
                        <div class="order_detail_row">
                            <span class="label">{{ t('center.orderNo') }}：</span>
                            <span class="value">{{ orderDetail.number }}</span>
                        </div>
                        <div class="order_detail_row">
                            <span class="label">{{ t('center.paymentType') }}：</span>
                            <span class="value">{{ orderDetail.payment_method_name || 'PayPal' }}</span>
                        </div>
                        <div class="order_detail_row">
                            <span class="label">{{ t('center.orderTime') }}：</span>
                            <span class="value">{{ formatDate(orderDetail.created_at) }}</span>
                        </div>
                        <div class="order_detail_row">
                            <span class="label">{{ t('center.deliver') }}：</span>
                            <span class="value">{{ orderDetail.shipping_method_name || '固定运费' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="box_subtitle"><label></label>{{ t('center.addressInfo') }}</div>
            <div class="box_address">
                <div class="box_address_item">{{ t('center.deliverAddress') }}</div>
                <div class="box_address_item">{{ t('center.payAddress') }}</div>
            </div>
            <div class="box_addressInfo">
                <div class="box_addressInfo_item">
                    <p class="box_addressInfo_item_desc">{{ t('center.name') }}：{{ orderDetail.payment_customer_name ||
                        '暂无' }}</p>
                    <p class="box_addressInfo_item_desc">{{ t('center.address') }}：{{
                        formatShippingAddress() }}</p>
                    <p class="box_addressInfo_item_desc">{{ t('center.pcode') }}：{{ orderDetail.shipping_zipcode || ''
                        }}</p>
                </div>
                <div class="box_addressInfo_item">
                    <p class="box_addressInfo_item_desc">{{ t('center.name') }}：{{ orderDetail.billing_address?.name ||
                        orderDetail.shipping_address?.name || '暂无' }}</p>
                    <p class="box_addressInfo_item_desc">{{ t('center.address') }}：{{
                        formatPaymentAddress() }}</p>
                    <p class="box_addressInfo_item_desc">{{ t('center.pcode') }}：{{ orderDetail.payment_zipcode || '' }}
                    </p>
                </div>
            </div>

            <div class="box_subtitle"><label></label>{{ '订单状态' }}</div>
            <div class="box_address">
                <div class="box_address_item">{{ '状态' }}</div>
                <div class="box_address_item">{{ '备注' }}</div>
                <div class="box_address_item">{{ '更新日期' }}</div>
            </div>
            <div class="box_goods" v-for="item in orderDetail.order_histories" :key="item.id">
                <div class="box_goods_info">
                    <div class="box_goods_info_name">{{ item.status_format }}</div>
                    <div class="box_goods_info_num">{{ item.comment || '无' }}</div>
                    <div class="box_goods_price">{{ item.updated_at }}</div>
                </div>
            </div>

            <div class="box_subtitle"><label></label>{{ t('center.note') }}</div>
            <div class="box_note">
                {{ orderDetail.comment || '无备注' }}
            </div>

            <div class="box_detail">
                <p>{{ t('center.goodsTotal') }}：{{ totalPrice }}</p>
                <p>{{ t('center.fee') }}：{{ feePrice }}</p>
                <p>{{ t('center.discount') }}：{{ discountPrice }}</p>
                <p v-if="orderDetail.discount_amount">{{ t('center.discount') }}：{{ orderDetail.discount_format }}</p>
            </div>
            <div class="box_line"></div>
            <div class="box_total">{{ t('center.goodsTotal') }}：<span>{{ totalPriceWithFee.value_format }}</span></div>

            <div class="box_action" v-if="orderDetail.status === 'unpaid'">
                <div class="box_action_item pay" @click="payOrder">{{ t('center.toPay') }}</div>
                <div class="box_action_item cancel" @click="cancelOrder">{{ t('center.cancel') }}</div>
            </div>
            <div class="box_action" v-else-if="orderDetail.status === 'shipped'">
                <div class="box_action_item pay" @click="confirmOrder">确认收货</div>
            </div>
        </div>

        <div v-else-if="!loading" class="empty_state">
            <p>订单详情加载失败</p>
            <div class="retry_btn" @click="loadOrderDetail">重新加载</div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { CancelOrder as CancelOrderAPI, CompleteOrder } from '@api/order';
import { GetUserOrderDetail } from '@api/account';

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const loading = ref(false);
const orderDetail = ref({});

const totalPrice = computed(() => {
    return orderDetail.value.order_totals.find(item => item.code == 'sub_total').value_format
});

const feePrice = computed(() => {
    return orderDetail.value.order_totals.find(item => item.code == 'shipping').value_format
});

const discountPrice = computed(() => {
    return orderDetail.value.order_totals.find(item => item.code == 'customer_discount').value_format
});

const totalPriceWithFee = computed(() => {
    return orderDetail.value.order_totals.find(item => item.code == 'order_total')
});

// 获取订单详情
const loadOrderDetail = async () => {
    const orderNumber = route.query.order_number;
    if (!orderNumber) {
        ElMessage.error('订单号不能为空');
        router.back();
        return;
    }

    try {
        console.log('orderNumber', orderNumber);
        loading.value = true;
        const response = await GetUserOrderDetail(orderNumber);
        console.log('订单详情:', response);
        response.showUrl = `http://************/${response.order.order_products[0].image}`

        orderDetail.value = response.order || {};
    } catch (error) {
        console.error('获取订单详情失败:', error);
        // ElMessage.error('获取订单详情失败');
    } finally {
        loading.value = false;
    }
};

// 获取产品图片
const getProductImage = (product) => {
    if (product && product.image) {
        return `http://************/${product.image}`;
    }
    return 'https://p0.ssl.img.360kuai.com/dmfd/158_88_75/t11508c75c8423c35fb6c06b871.webp?size=1024x819';
};

// 格式化日期
const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
};

// 格式化地址
const formatPaymentAddress = () => {
    if (!orderDetail) return '暂无地址';
    const parts = [
        orderDetail.payment_country || '',
        orderDetail.payment_zone || '',
        orderDetail.payment_city || '',
        orderDetail.payment_address_1 || '',
        orderDetail.payment_address_2 || ''
    ].filter(part => part.trim());

    return parts.join(' ') || '暂无地址';
};

// 格式化地址
const formatShippingAddress = () => {
    if (!orderDetail) return '暂无地址';
    const parts = [
        orderDetail.shipping_country || '',
        orderDetail.shipping_zone || '',
        orderDetail.shipping_city || '',
        orderDetail.shipping_address_1 || '',
        orderDetail.shipping_address_2 || ''
    ].filter(part => part.trim());

    return parts.join(' ') || '暂无地址';
};

// 获取订单状态文本
const getOrderStatusText = (status) => {
    const statusMap = {
        'unpaid': '待支付',
        'pending_payment': '待支付',
        'processing': '待发货',
        'shipped': '待收货',
        'completed': '已完成',
        'cancelled': '已取消',
        'refund_pending': '退款中'
    };
    return statusMap[status] || status;
};

// 返回上一页
const goBack = () => {
    router.back();
};

// 支付订单
const payOrder = () => {
    router.push(`/pay?order_number=${orderDetail.value.number}`);
};

// 取消订单
const cancelOrder = async () => {
    try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        await CancelOrderAPI(orderDetail.value.number);
        ElMessage.success('订单已取消');

        // 重新加载订单详情
        loadOrderDetail();
    } catch (error) {
        if (error !== 'cancel') {
            console.error('取消订单失败:', error);
            ElMessage.error('取消订单失败');
        }
    }
};

// 确认收货
const confirmOrder = async () => {
    try {
        await ElMessageBox.confirm('确定已收到货物吗？', '确认收货', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'info'
        });

        await CompleteOrder(orderDetail.value.number);
        ElMessage.success('订单已完成');

        // 重新加载订单详情
        loadOrderDetail();
    } catch (error) {
        if (error !== 'cancel') {
            console.error('确认收货失败:', error);
            ElMessage.error('确认收货失败');
        }
    }
};

// 申请售后
const applyAftersale = () => {
    console.log('申请售后', orderDetail);
    router.push(`/aftersale/apply?order_product_id=${orderDetail.value.id}`);
};

onMounted(() => {
    loadOrderDetail();
});
</script>
<style lang="scss" scoped>
@import url('./orderDetail.scss');

.box_title_back {
    cursor: pointer;

    &:hover {
        opacity: 0.7;
    }
}

.empty_state {
    text-align: center;
    padding: 3rem;
    color: #999;
    font-size: 1.4rem;

    .retry_btn {
        margin-top: 1rem;
        padding: 0.8rem 1.6rem;
        background: #6e4aeb;
        color: #fff;
        border-radius: 0.4rem;
        cursor: pointer;
        display: inline-block;

        &:hover {
            background: #5a3dc9;
        }
    }
}

.box_action_item,
.box_action .box_action_item {
    cursor: pointer;

    &:hover {
        opacity: 0.8;
    }
}
</style>
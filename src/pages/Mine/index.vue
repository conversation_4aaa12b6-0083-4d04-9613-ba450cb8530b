<template>
    <div class="mine_box">
        <div class="mine_box_section">
            <div class="mine_box_info" v-loading="userLoading">
                <img :src="userInfo.avatar || '@assets/avator.png'" class="mine_box_info_img" />
                <p class="mine_box_info_name">{{ userInfo.name || 'Guest' }}</p>
                <p class="mine_box_info_desc">{{ userInfo.email || 'Not logged in' }}</p>
            </div>
            <div class="mine_box_menu">
                <div v-for="item in menuList" :key="item.id" @click="onGoUrl(item.url)"
                    :class="['mine_box_menu_item', route.name == item.url ? 'active' : '']">
                    <img :src="item.icon" class="mine_box_menu_item_icon" />
                    {{ t(item.name) }}
                </div>
                <div class="mine_box_menu_action" @click="onLogout()" :disabled="logoutLoading">
                    {{ logoutLoading ? '退出中...' : '退出登录' }}
                </div>
            </div>
        </div>
        <div class="mine_box_main">
            <router-view></router-view>
        </div>
    </div>
</template>
<script setup>
import Home from '@assets/home.png';
import Info from '@assets/info.png';
import Pwd from '@assets/pwd.png';
import Order from '@assets/order.png';
import Loaction from '@assets/location.png';
import Collect from '@assets/collect.png';
import Message from '@assets/message.png';
import { ref, reactive, onMounted, provide } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import { GetAccountInfo } from '@api/account';
import { Logout } from '@api/auth';

const { t, locale } = useI18n();
const router = useRouter();
const route = useRoute();

const userLoading = ref(false);
const logoutLoading = ref(false);

// 用户信息
const userInfo = reactive({
    name: '',
    email: '',
    avatar: ''
});

const menuList = ref([
    {
        id: 0,
        name: 'center.home',
        icon: Home,
        url: 'Center'
    },
    {
        id: 1,
        name: 'center.info',
        icon: Info,
        url: 'User'
    },
    {
        id: 2,
        name: 'center.pwd',
        icon: Pwd,
        url: 'Pwd'
    },
    {
        id: 3,
        name: 'center.order',
        icon: Order,
        url: 'Order'
    },
    {
        id: 4,
        name: 'center.location',
        icon: Loaction,
        url: 'Address'
    },
    {
        id: 5,
        name: 'center.collect',
        icon: Collect,
        url: 'Collect'
    },
    {
        id: 6,
        name: 'center.message',
        icon: Message,
        url: 'Aftersale'
    }
]);

// 获取用户信息
const loadUserInfo = async () => {
    try {
        userLoading.value = true;
        const response = await GetAccountInfo();
        console.log('用户信息:', response);

        if (response && response.customer) {
            userInfo.name = response.customer.name || '';
            userInfo.email = response.customer.email || '';
            userInfo.avatar = response.customer.avatar || '';
        }
    } catch (error) {
        console.error('获取用户信息失败:', error);
        if (error.message?.includes('401')) {
            // 如果未登录，跳转到登录页
            router.push('/login');
        }
    } finally {
        userLoading.value = false;
    }
};

const onGoUrl = (url) => {
    router.push({ name: url })
}

// 退出登录
const onLogout = async () => {
    try {
        await ElMessageBox.confirm('确定要退出登录吗？', '确认退出', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
        });

        logoutLoading.value = true;

        // 调用退出登录接口
        await Logout();

        // 清除本地存储的认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('user_info');
        sessionStorage.removeItem('token');
        sessionStorage.removeItem('user_info');

        ElMessage.success('退出登录成功');

        // 跳转到登录页
        router.push('/login');

    } catch (error) {
        if (error !== 'cancel') {
            console.error('退出登录失败:', error);
            ElMessage.error('退出登录失败');
        }
    } finally {
        logoutLoading.value = false;
    }
};

// 提供刷新用户信息的方法给子组件使用
provide('refreshUserInfo', loadUserInfo);

onMounted(() => {
    loadUserInfo();
});

</script>
<style lang="scss" scoped>
@import url('./index.scss');

.mine_box_menu_action {
    cursor: pointer;
    transition: opacity 0.2s;

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}
</style>
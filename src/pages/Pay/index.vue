<template>
    <div class="pay_box">
        <div class="pay_box_main">
            <!-- 订单信息 -->
            <div class="order-info">
                <h2 class="order-title">{{ $t('payment.orderSubmitted', '订单提交成功，请付款') }}</h2>
                <div class="order-details">
                    <div class="order-item" v-if="orderInfo.orderNumber">
                        <span class="label">{{ $t('payment.orderNumber', '订单号') }}：</span>
                        <span class="value">{{ orderInfo.orderNumber }}</span>
                    </div>
                    <div class="order-item">
                        <span class="label">{{ $t('payment.amount', '支付金额') }}：</span>
                        <span class="value amount">{{ orderInfo.total_format }}</span>
                    </div>
                    <div class="order-item">
                        <span class="label">{{ $t('payment.amount', '支付方式') }}：</span>
                        <span class="value amount">{{ orderInfo.payment_method_name }}</span>
                    </div>
                </div>
            </div>

            <!-- 支付方式选择 -->
            <!-- <div class="payment-methods" v-loading="loading">
                <h3 class="section-title">{{ $t('payment.selectMethod', '选择支付方式') }}</h3>
                <div v-if="availablePaymentMethods.length === 0 && !loading" class="no-payment-methods">
                    <p>暂无可用的支付方式</p>
                </div>
                <el-radio-group v-else v-model="selectedPaymentMethod" class="payment-options">
                    <el-radio v-for="method in availablePaymentMethods" :key="method.id" :value="method.id"
                        class="payment-option">
                        <div class="payment-option-content">
                            <img :src="method.icon" :alt="method.name" class="payment-logo" />
                            <span>{{ method.name }}</span>
                        </div>
                    </el-radio>
                </el-radio-group>
            </div> -->

            <!-- Stripe 支付表单 -->
            <div v-if="selectedPaymentMethod === 'stripe'" class="payment-form-container">
                <StripePaymentForm :publishable-key="stripeConfig.publishableKey" :amount="orderInfo.total"
                    :order-number="orderInfo.orderNumber" :currency="orderInfo.currency || 'usd'"
                    @success="handlePaymentSuccess" @error="handlePaymentError" />
            </div>

            <!-- PayPal 支付容器 -->
            <div v-if="selectedPaymentMethod === 'paypal'" class="payment-form-container">
                <div id="paypal-button-container"></div>
            </div>

            <!-- 微信支付二维码 -->
            <div v-if="selectedPaymentMethod === 'wechat'" class="payment-form-container">
                <div class="wechat-payment">
                    <div class="qr-code-container">
                        <div id="wechat-qr-code"></div>
                    </div>
                    <p class="wechat-tip">{{ $t('payment.wechat.scanTip', '请使用微信扫描二维码完成支付') }}</p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import StripePaymentForm from '@/components/StripePaymentForm.vue'
import { GetOrderPayInfo } from '@/api/order'

const route = useRoute()
const router = useRouter()

// 响应式数据
const selectedPaymentMethod = ref('')
const loading = ref(false)
const availablePaymentMethods = ref([])
const orderInfo = ref({})
const orderNumber = route.query.order_number



// 支付配置
const stripeConfig = reactive({
    publishableKey: 'pk_test_51234567890abcdef'
})

// 支付方式映射配置
const paymentMethodsConfig = {
    'paypal': {
        id: 'paypal',
        name: 'PayPal',
        icon: '/src/assets/paypal.png',
        component: 'paypal'
    },
    'stripe': {
        id: 'stripe',
        name: 'Stripe',
        icon: '/src/assets/<EMAIL>',
        component: 'stripe'
    },
    'bitpay': {
        id: 'bitpay',
        name: 'BitPay',
        icon: '/src/assets/<EMAIL>',
        component: 'bitpay'
    },
    'wepay': {
        id: 'wepay',
        name: '微信支付',
        icon: '/src/assets/<EMAIL>',
        component: 'wechat'
    }
}


// 获取订单详情信息
const fetchOrderDetail = async () => {
    if (!orderNumber) {
        ElMessage.error('订单号不能为空')
        return
    }

    try {
        loading.value = true
        const response = await GetOrderPayInfo(orderNumber)
        console.log('订单详情:', response)

        if (response.order) {
            const order = response.order
            orderInfo.value = order

            // 根据订单中的支付方式代码设置可用支付方式
            if (order.payment_method_code) {
                const paymentCode = order.payment_method_code
                const paymentName = order.payment_method_name || ''

                // 检查支付方式是否在配置中
                if (paymentMethodsConfig[paymentCode]) {
                    availablePaymentMethods.value = [{
                        ...paymentMethodsConfig[paymentCode],
                        code: paymentCode,
                        name: paymentName || paymentMethodsConfig[paymentCode].name
                    }]
                    selectedPaymentMethod.value = paymentMethodsConfig[paymentCode].id
                } else {
                    // 如果支付方式不在配置中，显示所有可用支付方式
                    availablePaymentMethods.value = Object.values(paymentMethodsConfig)
                    selectedPaymentMethod.value = availablePaymentMethods.value[0]?.id || ''
                }
            } else {
                // 如果订单没有指定支付方式，显示所有可用支付方式
                availablePaymentMethods.value = Object.values(paymentMethodsConfig)
                selectedPaymentMethod.value = availablePaymentMethods.value[0]?.id || ''
            }
        }
    } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败，请检查订单号是否正确')
    } finally {
        loading.value = false
    }
}


// 处理支付成功
const handlePaymentSuccess = (result) => {
    console.log('支付成功:', result)
    ElMessage.success('支付成功！')

    // 跳转到支付成功页面
    router.push({
        path: '/payment/success',
        query: {
            orderNumber: result.orderNumber,
            paymentMethod: selectedPaymentMethod.value
        }
    })
}

// 处理支付错误
const handlePaymentError = (error) => {
    console.error('支付失败:', error)
    ElMessage.error(error.message || '支付失败，请重试')
}

// 组件挂载时初始化数据
onMounted(() => {
    fetchOrderDetail()
})
</script>

<style lang="scss" scoped>
@import url('./index.scss');

.no-payment-methods {
    text-align: center;
    padding: 40px 20px;
    color: #999;

    p {
        margin: 0;
        font-size: 16px;
    }
}

.payment-methods {
    .el-loading-mask {
        border-radius: 8px;
    }
}
</style>
<template>
    <div class="goods_view">
        <div class="goods_view_title">{{ t('goods.title') }}</div>
        <goodsList :products="productList" />
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { GetProductList } from '@api/product';
import goodsList from '@components/goodsList.vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const productList = ref([]);

// 获取商品列表数据
const loadProductList = async () => {
    try {
        const res = await GetProductList();
        console.log('商品列表数据', res);
        if (res && res.items) {
            productList.value = res.items;
        }
    } catch (error) {
        console.error('获取商品列表失败:', error);
    }
};

onMounted(() => {
    loadProductList();
});
</script>
<style lang="scss" scoped>
@import url('./list.scss');
</style>
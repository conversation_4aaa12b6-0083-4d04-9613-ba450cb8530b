<template>
    <div class="goods_box">
        <div class="goods_box_title">{{ t('product.title') }} / {{ productDetail.name || 'Loading...' }}</div>
        <div v-if="productDetail.id">
            <div class="goods_box_info">
                <div class="goods_box_info_img">
                    <div class="goods_box_info_img_list">
                        <div v-for="(item, index) in productDetail.images" :key="index"
                            class="goods_box_info_img_list_single" @click="onChangeImg(item)">
                            <img :src="item.thumb" class="goods_box_info_img_list_single_img" />
                        </div>
                    </div>
                    <div class="goods_box_info_img_big">
                        <img class="goods_box_info_img_big_img" :src="currentImg" />
                    </div>
                </div>
                <div class="goods_box_info_base">
                    <h4 class="goods_box_info_base_name">{{ productDetail.name }}</h4>
                    <p class="goods_box_info_base_price">
                        {{ currentSku.price_format }}
                    </p>
                    <div class="goods_box_info_base_property">
                        <label>库存：</label>
                        <span>{{ hasStock ? '有货' : '无货' }}</span>
                    </div>
                    <div class="goods_box_info_base_property">
                        <label>SKU：</label>
                        <span>{{ currentSku.sku }}</span>
                    </div>
                    <div class="goods_box_info_base_property">
                        <label>型号：</label>
                        <span>{{ currentSku.model }}</span>
                    </div>
                    <div class="goods_box_info_base_action">
                        <div class="goods_box_info_base_action_item addcart" @click="addToCart">
                            加入购物车
                            <img src="@assets/arrow-right.png" class="goods_box_info_base_action_item_icon" />
                        </div>
                        <div class="goods_box_info_base_action_item buynow" @click="buyNow">
                            立即购买
                            <img src="@assets/cart.png" class="goods_box_info_base_action_item_icon" />
                        </div>
                    </div>
                    <div class="goods_box_info_base_collect" @click="toggleWishlist">
                        <img src="@assets/collect.png" class="goods_box_info_base_collect_icon" />
                        <span>{{ productDetail.in_wishlist ? '取消收藏' : '收藏' }}</span>
                    </div>
                </div>
            </div>
            <div v-if="productDetail.description" class="goods_box_description" v-html="productDetail.description">
            </div>
        </div>
        <div v-else class="loading">
            加载中...
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { GetProductDetail } from '@api/product';
import { AddToCart, UnselectCartItems, GetCartList } from '@api/cart';
import { AddToWishlist, RemoveFromWishlist } from '@api/wishlist';
import { ElMessage, ElMessageBox } from 'element-plus';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const productDetail = ref({});
const currentImg = ref('');
const quantity = ref(1);
const loading = ref(false);

// 计算属性：是否有库存
const hasStock = computed(() => {
    return productDetail.value.skus?.[0]?.quantity > 0;
});

// 计算属性：当前SKU
const currentSku = computed(() => {
    return productDetail.value.skus?.[0] || {};
});

// 获取商品详情数据
const loadProductDetail = async () => {
    try {
        const productId = route.params.id;
        console.log('商品ID:', productId);

        let res = await GetProductDetail(productId);
        console.log('商品详情数据', res);
        res = res.product

        if (res) {
            productDetail.value = res;
            // 设置默认显示的图片为第一张
            if (res.images && res.images.length > 0) {
                currentImg.value = res.images[0].preview;
            }
        }
    } catch (error) {
        console.error('获取商品详情失败:', error);
        // 可以添加错误提示或重定向到404页面
    }
};

// 切换显示的图片
const onChangeImg = (img) => {
    currentImg.value = img.preview;
};

// 添加到购物车
const addToCart = async () => {
    if (loading.value) return;

    try {
        // 检查商品数据是否存在
        if (!currentSku.value.id) {
            ElMessage.error('商品信息加载中，请稍后再试');
            return;
        }

        // 检查库存
        if (!hasStock.value) {
            ElMessage.error('商品库存不足');
            return;
        }

        if (quantity.value > currentSku.value.quantity) {
            ElMessage.error(`库存不足，最多可购买 ${currentSku.value.quantity} 件`);
            return;
        }

        loading.value = true;

        const cartData = {
            sku_id: currentSku.value.id,
            quantity: quantity.value,
            buy_now: false
        };

        const response = await AddToCart(cartData);
        console.log('加入购物车响应:', response);

        ElMessage.success('商品已添加到购物车');

    } catch (error) {
        console.error('加入购物车失败:', error);

        // 处理不同类型的错误
        if (error.response) {
            const message = error.response.data?.message || '加入购物车失败';
            ElMessage.error(message);
        } else if (error.message?.includes('401')) {
            ElMessage.error('请先登录');
            router.push('/login');
        } else {
            ElMessage.error('网络错误，请稍后重试');
        }
    } finally {
        loading.value = false;
    }
};

// 清空其他选中的商品
const clearOtherSelectedItems = async () => {
    try {
        // 获取当前购物车列表
        const cartResponse = await GetCartList();
        if (cartResponse && cartResponse.carts) {
            // 找到所有已选中的商品ID
            const selectedCartIds = cartResponse.carts
                .filter(item => item.selected)
                .map(item => item.id);

            if (selectedCartIds.length > 0) {
                // 取消选中所有商品
                await UnselectCartItems(selectedCartIds);
                console.log('已清空其他选中商品');
            }
        }
    } catch (error) {
        console.error('清空选中商品失败:', error);
        // 这个错误不影响主流程，所以不显示给用户
    }
};

// 立即购买
const buyNow = async () => {
    if (loading.value) return;

    try {
        // 检查商品数据是否存在
        if (!currentSku.value.id) {
            ElMessage.error('商品信息加载中，请稍后再试');
            return;
        }

        // 检查库存
        if (!hasStock.value) {
            ElMessage.error('商品库存不足');
            return;
        }

        if (quantity.value > currentSku.value.quantity) {
            ElMessage.error(`库存不足，最多可购买 ${currentSku.value.quantity} 件`);
            return;
        }

        loading.value = true;

        // 先清空其他选中的商品
        await clearOtherSelectedItems();

        const cartData = {
            sku_id: currentSku.value.id,
            quantity: quantity.value,
            buy_now: true
        };

        const response = await AddToCart(cartData);
        console.log('立即购买响应:', response);

        ElMessage.success('正在跳转到结算页面...');

        // 跳转到确认订单页面的第二步
        router.push({ path: '/confirm', query: { step: 2 } });

    } catch (error) {
        console.error('立即购买失败:', error);

        // 处理不同类型的错误
        if (error.response) {
            const message = error.response.data?.message || '立即购买失败';
            ElMessage.error(message);
        } else if (error.message?.includes('401')) {
            ElMessage.error('请先登录');
            router.push('/login');
        } else {
            ElMessage.error('网络错误，请稍后重试');
        }
    } finally {
        loading.value = false;
    }
};

// 验证数量输入
const validateQuantity = () => {
    const maxQuantity = currentSku.value.quantity || 999;

    if (isNaN(quantity.value) || quantity.value < 1) {
        quantity.value = 1;
    } else if (quantity.value > maxQuantity) {
        quantity.value = maxQuantity;
        ElMessage.warning(`最多只能购买 ${maxQuantity} 件`);
    }

    // 确保是整数
    quantity.value = Math.floor(quantity.value);
};

// 切换收藏状态
const toggleWishlist = async () => {
    if (loading.value) return;

    try {
        loading.value = true;
        const productId = productDetail.value.id;

        let response;
        if (productDetail.value.in_wishlist) {
            // 取消收藏 - 需要使用 wishlist_id
            const wishlistId = productDetail.value.wishlist_id;
            if (!wishlistId) {
                ElMessage.error('收藏信息错误');
                return;
            }
            response = await RemoveFromWishlist(wishlistId);
        } else {
            // 添加收藏 - 使用 product_id
            response = await AddToWishlist(productId);
        }

        console.log('切换收藏状态响应:', response);

        ElMessage.success(productDetail.value.in_wishlist ? '已取消收藏' : '已收藏');

        // 更新本地状态
        productDetail.value.in_wishlist = !productDetail.value.in_wishlist;

        // 如果是新增收藏，更新 wishlist_id
        if (productDetail.value.in_wishlist && response && response.wishlist_id) {
            productDetail.value.wishlist_id = response.wishlist_id;
        }

    } catch (error) {
        console.error('切换收藏失败:', error);

        // 处理不同类型的错误
        if (error.response) {
            const message = error.response.data?.message || '切换收藏失败';
            ElMessage.error(message);
        } else if (error.message?.includes('401')) {
            ElMessage.error('请先登录');
            router.push('/login');
        } else {
            ElMessage.error('网络错误，请稍后重试');
        }
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    loadProductDetail();
});
</script>
<style lang="scss" scoped>
@import url('./index.scss');

.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: #666;
}

.goods_box_description {
    padding: 2rem 3rem;
    margin-top: 2rem;
    background: #ffffff;
    border-radius: 1rem;
    line-height: 1.6;
    color: #333;

    /* 富文本内容样式 */
    :deep(h1),
    :deep(h2),
    :deep(h3),
    :deep(h4),
    :deep(h5),
    :deep(h6) {
        margin: 1.5rem 0 1rem 0;
        font-weight: 600;
        color: #242426;
    }

    :deep(h1) {
        font-size: 2.4rem;
    }

    :deep(h2) {
        font-size: 2.1rem;
    }

    :deep(h3) {
        font-size: 1.8rem;
    }

    :deep(h4) {
        font-size: 1.6rem;
    }

    :deep(h5) {
        font-size: 1.4rem;
    }

    :deep(h6) {
        font-size: 1.2rem;
    }

    :deep(p) {
        margin: 1rem 0;
        font-size: 1.4rem;
        line-height: 1.8;
    }

    :deep(ul),
    :deep(ol) {
        margin: 1rem 0;
        padding-left: 2rem;

        li {
            margin: 0.5rem 0;
            font-size: 1.4rem;
            line-height: 1.6;
        }
    }

    :deep(img) {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
        margin: 1rem 0;
    }

    :deep(blockquote) {
        margin: 1.5rem 0;
        padding: 1rem 1.5rem;
        background: #f8f9fa;
        border-left: 4px solid #6e4aeb;
        border-radius: 0.5rem;
        font-style: italic;
    }

    :deep(table) {
        width: 100%;
        border-collapse: collapse;
        margin: 1.5rem 0;

        th,
        td {
            padding: 0.8rem 1rem;
            border: 1px solid #ddd;
            text-align: left;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
        }
    }

    :deep(code) {
        background: #f1f3f4;
        padding: 0.2rem 0.4rem;
        border-radius: 0.3rem;
        font-family: 'Courier New', monospace;
        font-size: 0.9em;
    }

    :deep(pre) {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        overflow-x: auto;
        margin: 1rem 0;

        code {
            background: none;
            padding: 0;
        }
    }

    :deep(a) {
        color: #6e4aeb;
        text-decoration: none;

        &:hover {
            text-decoration: underline;
        }
    }

    :deep(strong),
    :deep(b) {
        font-weight: 600;
    }

    :deep(em),
    :deep(i) {
        font-style: italic;
    }
}

/* 数量选择器样式 */
.quantity-selector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quantity-btn {
    width: 3rem;
    height: 3rem;
    border: 1px solid #ddd;
    background: #fff;
    border-radius: 0.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.6rem;
    font-weight: 600;
    color: #666;
    transition: all 0.2s;

    &:hover:not(:disabled) {
        border-color: #6e4aeb;
        color: #6e4aeb;
    }

    &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
}

.quantity-input {
    width: 6rem;
    height: 3rem;
    border: 1px solid #ddd;
    border-radius: 0.4rem;
    text-align: center;
    font-size: 1.4rem;

    &:focus {
        outline: none;
        border-color: #6e4aeb;
    }
}

/* 加载状态和禁用状态样式 */
:deep(.goods_box_info_base_action_item.loading),
:deep(.goods_box_info_base_action_item.disabled) {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

:deep(.goods_box_info_base_action_item.disabled) {
    background: #f5f5f5;
    color: #999;
}
</style>
<template>
    <div class="cart_view">
        <p class="cart_view_title">{{ t('cart.title') }}</p>
        <div class="cart_view_step">
            <div :class="['cart_view_step_item', step <= 3 ? 'active' : '']">
                <img v-if="step > 1" src="@assets/checked.png" class="cart_view_step_item_img" />
                <div v-if="step == 1" class="cart_view_step_item_dot">
                    1
                </div>
                {{ t('cart.cart') }}
            </div>
            <div :class="['cart_view_step_line', step <= 2 ? 'active' : '']"></div>
            <div :class="['cart_view_step_item', step == 2 ? 'active' : '']">
                <img v-if="step > 2" src="@assets/checked.png" class="cart_view_step_item_img" />
                <div v-else :class="['cart_view_step_item_dot', step !== 2 ? 'disable' : '']">
                    2
                </div>
                {{ t('cart.confirm') }}
            </div>
            <div :class="['cart_view_step_line', step == 3 ? 'active' : '']"></div>
            <div :class="['cart_view_step_item', step == 3 ? 'active' : '']">
                <div :class="['cart_view_step_item_dot', step !== 3 ? 'disable' : '']">
                    3
                </div>
                {{ t('cart.pay') }}
            </div>
        </div>
        <div class="cart_view_pay" v-if="step == 3">
            <img src="@assets/payIcon.png" class="cart_view_pay_img" />
            <div class="cart_view_pay_main">
                <p class="cart_view_pay_main_title">{{ t('cart.tip') }}</p>
                <div class="cart_view_pay_main_info">
                    <div class="cart_view_pay_main_info_item mb8">
                        {{ t('cart.orderNo') }}：
                        <span>{{ paymentInfo.orderNumber }}</span>
                    </div>
                    <div class="cart_view_pay_main_info_item mb15">
                        {{ t('cart.payAmount') }}：
                        <span class="red">{{ paymentInfo.total_format }}</span>
                    </div>
                    <div class="cart_view_pay_main_info_item">
                        {{ t('cart.payType') }}：
                        <img :src="selectedPaymentIcon" :alt="selectedPaymentName" />
                    </div>
                </div>
                <div class="cart_view_pay_main_action">
                    <div class="cart_view_pay_main_action_item" @click="handlePayment">
                        {{ t('cart.toPay') }}
                        <img src="@assets/check.png" />
                    </div>
                    <!-- <div class="cart_view_pay_main_action_item">
                        {{ t('cart.credit') }}
                        <img src="@assets/credit.png" />
                    </div> -->
                </div>
            </div>
        </div>
        <div class="cart_view_box" v-else>
            <div class="cart_view_box_cartList" v-if="step == 1">
                <div class="cart_view_box_title">{{ t('cart.subtitle') }}</div>
                <div class="cart_view_box_thead">
                    <div class="cart_view_box_thead_th w117">
                        <el-checkbox v-model="chooseAll" @change="handleSelectAll" class="check">
                            {{ t('cart.chooseAll') }}
                        </el-checkbox>
                    </div>
                    <div class="cart_view_box_thead_th w377">{{ t('cart.subtitle') }}</div>
                    <div class="cart_view_box_thead_th w114">{{ t('cart.num') }}</div>
                    <div class="cart_view_box_thead_th w147">{{ t('cart.fee') }}</div>
                    <div class="cart_view_box_thead_th w1">{{ t('cart.operate') }}</div>
                </div>
                <div v-if="loading" class="loading-state">
                    加载中...
                </div>
                <div v-else-if="cartList.length === 0" class="empty-state">
                    购物车为空
                </div>
                <div v-else>
                    <div class="cart_view_box_tr" v-for="item in cartList" :key="item.cart_id">
                        <div class="cart_view_box_tr_td w117">
                            <el-checkbox :model-value="item.selected"
                                @change="(checked) => handleSelectItem(item.cart_id, checked)"></el-checkbox>
                        </div>
                        <div class="cart_view_box_tr_td w377">
                            <img :src="item.image_url" />
                            {{ item.name_format }}
                        </div>
                        <div class="cart_view_box_tr_td w114">
                            <el-input-number :model-value="item.quantity" :min="1" :max="item.stock"
                                @change="(value) => handleUpdateQuantity(item.cart_id, value)" size="small" />
                        </div>
                        <div class="cart_view_box_tr_td w147">{{ item.price_format }}</div>
                        <div class="cart_view_box_tr_td w1">
                            <div class="cart_view_box_tr_td_action" @click="handleRemoveItem(item.cart_id)">
                                {{ t('cart.del') }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="cart_view_box_container" v-else>
                <div class="cart_view_box_title">{{ t('cart.address') }}</div>

                <!-- 地址列表 -->
                <div v-if="addressList.length > 0">
                    <div v-for="address in addressList" :key="address.id" class="cart_view_box_address"
                        :class="{ 'selected': selectedAddressId === address.id }"
                        @click="handleSelectAddress(address.id)">
                        <div class="cart_view_box_address_user">
                            {{ address.name }}&nbsp;&nbsp;{{ address.phone }}
                            <span v-if="address.is_default" class="default-tag">{{ t('cart.default') }}</span>
                        </div>
                        <div class="cart_view_box_address_detail">{{ address.full_address }}</div>
                        <div class="cart_view_box_address_action">
                            <img src="@assets/edit.png" class="cart_view_box_address_action_icon"
                                @click.stop="handleEditAddress(address.id)" />
                            <img src="@assets/trash.png" class="cart_view_box_address_action_icon"
                                @click.stop="handleDeleteAddress(address.id)" />
                        </div>
                    </div>
                </div>

                <div class="cart_view_box_add" @click="handleAddAddress">
                    <img src="@assets/add.png" class="cart_view_box_add_img" />
                    {{ t('cart.add') }}
                </div>
                <div class="cart_view_box_title">{{ t('cart.paymentType') }}</div>
                <div class="cart_view_box_payment">
                    <div v-for="payment in paymentMethods" :key="payment.id" class="cart_view_box_payment_item"
                        :class="{ 'selected': selectedPaymentMethod === payment.id }"
                        @click="handleSelectPayment(payment.id)">
                        <img :src="payment.icon" :alt="payment.name" class="cart_view_box_payment_item_icon" />
                    </div>
                </div>
                <div class="cart_view_box_title">{{ t('cart.deliver') }}</div>
                <div class="cart_view_box_deliver">
                    <img src="@assets/icon.png" class="cart_view_box_deliver_icon" />
                    <div class="cart_view_box_deliver_main">
                        <div class="cart_view_box_deliver_main_tip1">{{ t('cart.tip1') }}</div>
                        <div class="cart_view_box_deliver_main_tip2">{{ t('cart.tip2') }}</div>
                    </div>
                </div>
                <div class="cart_view_box_title">{{ t('cart.note') }}</div>
                <textarea class="cart_view_box_textarea" :placeholder="t('cart.noteplaceholder')" />
            </div>
            <div class="cart_view_box_list">
                <div class="cart_view_box_titles">
                    {{ step == 1 ? t('cart.total') : t('cart.tips') }}
                    <span class="cart_view_box_titles_num">{{ selectedCount }}</span>
                </div>
                <div class="cart_view_box_line"></div>
                <div v-if="step == 2">
                    <div class="cart_view_box_list_item" v-for="item in selectedItems" :key="item.cart_id">
                        <img :src="item.image_url" class="cart_view_box_list_item_img" />
                        <div class="cart_view_box_list_item_main">
                            <p class="cart_view_box_list_item_main_name">{{ item.name_format }}</p>
                            <p class="cart_view_box_list_item_main_info">
                                {{ item.price_format }}
                                <span>x {{ item.quantity }}</span>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="cart_view_box_info">
                    <span>{{ t('cart.all') }}</span>
                    {{ totalCount }}
                </div>
                <div class="cart_view_box_info">
                    <span>{{ t('cart.choose') }}</span>
                    {{ selectedCount }}
                </div>
                <div class="cart_view_box_lines"></div>
                <div class="cart_view_box_all">
                    {{ t('cart.total') }}
                    <span>{{ totalAmount }}</span>
                </div>
                <div class="cart_view_box_btn" @click="handleNext"
                    :class="{ 'disabled': step == 1 && selectedCount === 0 }">
                    {{ step == 1 ? t('cart.toPayment') : t('cart.submit') }}
                </div>
            </div>
        </div>

        <!-- 地址弹框 -->
        <AddressDialog v-model:visible="addressDialogVisible" :address-id="editAddressId"
            @success="handleAddressSuccess" />
    </div>
</template>
<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
    GetCartList,
    SelectCartItems,
    UnselectCartItems,
    UpdateCart,
    RemoveCartItem,
    GetMiniCart
} from '@api/cart';
import { GetCheckoutInfo, ConfirmOrder, UpdateCheckout } from '@api/checkout';
import { GetAddressList, DeleteAddress } from '@api/address';
import { GetOrderPayInfo } from '@api/order';
// import { PayWithPayPal, PayWithStripe, PayWithBitPay, PayWithWePay, GetPaymentStatus } from '@api/payment';
import { PaymentFactory } from '@utils/payment';
import AddressDialog from '@components/AddressDialog.vue';

const { t } = useI18n();
const route = useRoute();
const router = useRouter();

const step = ref(parseInt(route.query.step) || 1);
const cartList = ref([]);
const miniCartData = ref({});
const loading = ref(false);
const chooseAll = ref(false);
const paymentInfo = ref(null);

// 地址相关数据
const addressList = ref([]);
const selectedAddressId = ref(null);
const addressDialogVisible = ref(false);
const editAddressId = ref(null);
const payInfo = ref(null);


// 支付方式相关数据
const selectedPaymentMethod = ref('paypal');
const paymentMethods = ref([
    {
        id: 'paypal',
        name: 'PayPal',
        icon: '/src/assets/paypal.png'
    },
    {
        id: 'stripe',
        name: 'Stripe',
        icon: '/src/assets/<EMAIL>'
    },
    {
        id: 'bitpay',
        name: 'BitPay',
        icon: '/src/assets/<EMAIL>'
    },
    {
        id: 'wepay',
        name: 'WePay',
        icon: '/src/assets/<EMAIL>'
    }
]);

// 计算属性
const selectedItems = computed(() => {
    return cartList.value.filter(item => item.selected);
});

const selectedCount = computed(() => {
    return selectedItems.value.length;
});

const totalCount = computed(() => {
    return cartList.value.length;
});

const totalAmount = computed(() => {
    const total = selectedItems.value.reduce((sum, item) => {
        return sum + (item.price * item.quantity);
    }, 0);
    return `$${total.toFixed(2)}`;
});

// 选中的支付方式图标和名称
const selectedPaymentIcon = computed(() => {
    const payment = paymentMethods.value.find(p => p.id === selectedPaymentMethod.value);
    return payment ? payment.icon : paymentMethods.value[0].icon;
});

const selectedPaymentName = computed(() => {
    const payment = paymentMethods.value.find(p => p.id === selectedPaymentMethod.value);
    return payment ? payment.name : paymentMethods.value[0].name;
});


// 监听步骤变化
watch(() => route.query.step, (newStep) => {
    step.value = parseInt(newStep) || 1;
    if (step.value === 2) {
        loadMiniCart();
    }
});

// 监听选中状态变化，更新全选状态
watch(cartList, () => {
    chooseAll.value = cartList.value.length > 0 && cartList.value.every(item => item.selected);
}, { deep: true });

// 获取购物车列表
const loadCartList = async () => {
    try {
        loading.value = true;
        const response = await GetCartList();
        console.log('购物车列表:', response);

        if (response && response.carts) {
            cartList.value = response.carts.map(item => ({
                cart_id: item.cart_id,
                name_format: item.name_format,
                image_url: item.image_url,
                price: item.price,
                price_format: item.price_format,
                quantity: item.quantity,
                stock: item.stock,
                selected: item.selected || false,
                sku_id: item.sku_id
            }));
        }
    } catch (error) {
        console.error('获取购物车列表失败:', error);
        ElMessage.error('获取购物车列表失败');
    } finally {
        loading.value = false;
    }
};

// 获取迷你购物车信息（第二步用）
const loadMiniCart = async () => {
    try {
        const response = await GetMiniCart();
        console.log('迷你购物车:', response);

        if (response) {
            miniCartData.value = response;
            // 更新选中的商品列表
            if (response.carts) {
                cartList.value = response.carts.map(item => ({
                    cart_id: item.cart_id,
                    name_format: item.name_format,
                    image_url: item.image_url,
                    price: item.price,
                    price_format: item.price_format,
                    quantity: item.quantity,
                    selected: true
                }));
            }
        }
    } catch (error) {
        console.error('获取迷你购物车失败:', error);
        ElMessage.error('获取迷你购物车失败');
    }
};

// 全选/取消全选
const handleSelectAll = async (checked) => {
    try {
        const cartIds = cartList.value.map(item => item.cart_id);

        if (checked) {
            await SelectCartItems(cartIds);
            cartList.value.forEach(item => item.selected = true);
        } else {
            await UnselectCartItems(cartIds);
            cartList.value.forEach(item => item.selected = false);
        }
    } catch (error) {
        console.error('批量选择失败:', error);
        ElMessage.error('操作失败');
        chooseAll.value = !checked; // 恢复状态
    }
};

// 选中/取消选中单个商品
const handleSelectItem = async (cartId, checked) => {
    try {
        if (checked) {
            await SelectCartItems([cartId]);
        } else {
            await UnselectCartItems([cartId]);
        }

        const item = cartList.value.find(item => item.cart_id === cartId);
        if (item) {
            item.selected = checked;
        }
    } catch (error) {
        console.error('选择商品失败:', error);
        ElMessage.error('操作失败');

        // 恢复状态
        const item = cartList.value.find(item => item.cart_id === cartId);
        if (item) {
            item.selected = !checked;
        }
    }
};

// 更新商品数量
const handleUpdateQuantity = async (cartId, quantity) => {
    try {
        const item = cartList.value.find(item => item.cart_id === cartId);
        if (!item) return;

        await UpdateCart(cartId, {
            quantity: quantity,
            sku_id: item.sku_id
        });

        item.quantity = quantity;
        ElMessage.success('数量更新成功');
    } catch (error) {
        console.error('更新数量失败:', error);
        ElMessage.error('更新数量失败');

        // 刷新列表
        loadCartList();
    }
};

// 删除商品
const handleRemoveItem = async (cartId) => {
    try {
        await ElMessageBox.confirm('确定要删除这个商品吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        });

        await RemoveCartItem(cartId);
        cartList.value = cartList.value.filter(item => item.cart_id !== cartId);
        ElMessage.success('商品已删除');
    } catch (error) {
        if (error === 'cancel') return;

        console.error('删除商品失败:', error);
        ElMessage.error('删除商品失败');
    }
};

// 下一步
const handleNext = () => {
    if (step.value === 1) {
        if (selectedCount.value === 0) {
            ElMessage.warning('请选择商品');
            return;
        }
        router.push({ path: '/confirm', query: { step: 2 } });
    } else if (step.value === 2) {
        handleSubmitOrder();
    }
};

// 提交订单
const handleSubmitOrder = async () => {
    try {
        loading.value = true;
        const response = await ConfirmOrder();
        console.log('订单提交结果:', response);

        if (response && response.number) {
            paymentInfo.value = { orderNumber: response.number, ...response };
            router.push({ path: '/confirm', query: { step: 3 } });

            GetOrderPayInfo(response.number).then(res => {
                console.log('支付信息', res);
                payInfo.value = res.payment_setting;
            })
        }
    } catch (error) {
        console.error('提交订单失败:', error);
        // ElMessage.error('提交订单失败');
    } finally {
        loading.value = false;
    }
};

// 处理支付
const handlePayment = async () => {
    try {
        const orderNumber = paymentInfo.value.orderNumber;
        const amount = parseFloat(paymentInfo.value.total);

        if (!orderNumber) {
            ElMessage.error('订单信息不完整');
            return;
        }

        // 根据选中的支付方式进行支付
        await processPayment(selectedPaymentMethod.value, orderNumber, amount);
    } catch (error) {
        console.error('支付处理失败:', error);
        ElMessage.error('支付处理失败');
    }
};

// 加载地址列表
const loadAddressList = async () => {
    try {
        const response = await GetAddressList();
        console.log('地址列表:', response);

        if (response && response.addresses) {
            addressList.value = response.addresses.map(address => ({
                id: address.id,
                name: address.name,
                phone: address.phone,
                full_address: address.full_address,
                is_default: address.is_default
            }));

            // 设置默认选中地址
            const defaultAddress = addressList.value.find(addr => addr.is_default);
            if (defaultAddress) {
                selectedAddressId.value = defaultAddress.id;
            } else if (addressList.value.length > 0) {
                selectedAddressId.value = addressList.value[0].id;
            }
        }
    } catch (error) {
        // console.error('获取地址列表失败:', error);
        // ElMessage.error('获取地址列表失败');
    }
};

// 选择地址
const handleSelectAddress = (addressId) => {
    selectedAddressId.value = addressId;
};

// 添加地址
const handleAddAddress = () => {
    editAddressId.value = null;
    addressDialogVisible.value = true;
};

// 编辑地址
const handleEditAddress = (addressId) => {
    editAddressId.value = addressId;
    addressDialogVisible.value = true;
};

// 删除地址
const handleDeleteAddress = async (addressId) => {
    try {
        await ElMessageBox.confirm('确定要删除这个地址吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        });

        await DeleteAddress(addressId);
        ElMessage.success('地址已删除');

        // 重新加载地址列表
        loadAddressList();

        // 如果删除的是当前选中的地址，重置选中状态
        if (selectedAddressId.value === addressId) {
            selectedAddressId.value = null;
        }
    } catch (error) {
        if (error === 'cancel') return;

        console.error('删除地址失败:', error);
        ElMessage.error('删除地址失败');
    }
};

// 地址操作成功回调
const handleAddressSuccess = () => {
    loadAddressList();
};

// 选择支付方式
const handleSelectPayment = (paymentId) => {
    selectedPaymentMethod.value = paymentId;
    UpdateCheckout({
        payment_method_code: paymentId
    });
    console.log('选择支付方式:', paymentId);
};

// 处理具体的支付流程
const processPayment = async (paymentMethod, orderNumber, amount) => {
    try {
        switch (paymentMethod) {
            case 'paypal':
                await handlePayPalPayment(orderNumber, amount);
                break;
            case 'stripe':
                await handleStripePayment(orderNumber, amount);
                break;
            case 'bitpay':
                await handleBitPayPayment(orderNumber, amount);
                break;
            case 'wepay':
                await handleWePayPayment(orderNumber, amount);
                break;
            default:
                throw new Error(`不支持的支付方式: ${paymentMethod}`);
        }
    } catch (error) {
        console.error(`${paymentMethod}支付失败:`, error);
        throw error;
    }
};

// PayPal支付处理
const handlePayPalPayment = async (orderNumber, amount,) => {
    try {
        const paypal = PaymentFactory.createPayment('paypal', { clientId: payInfo.value.live_client_id, ...payInfo.value });

        // 创建PayPal支付按钮容器
        const containerId = 'paypal-payment-container';
        let container = document.getElementById(containerId);

        if (!container) {
            container = document.createElement('div');
            container.id = containerId;
            container.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                z-index: 9999;
                min-width: 300px;
            `;
            document.body.appendChild(container);
        }

        container.innerHTML = `
            <div id="paypal-button-container"></div>
            <button onclick="this.parentElement.remove()" style="margin-top: 10px; padding: 8px 16px; background: #ccc; border: none; border-radius: 4px; cursor: pointer;width:100%">取消</button>
        `;

        const result = await paypal.createPayment(amount, orderNumber);

        if (result && result.success) {
            // 调用后端API确认支付
            // await PayWithPayPal(orderNumber, {
            //     paypalOrderId: result.orderId,
            //     paymentId: result.paymentId
            // });

            ElMessage.success('PayPal支付成功！');
            container.remove();
            router.push('/order');
        }
    } catch (error) {
        console.error('PayPal支付失败:', error);
        ElMessage.error('PayPal支付失败');
        throw error;
    }
};

// Stripe支付处理
const handleStripePayment = async (orderNumber, amount) => {
    try {
        const stripe = PaymentFactory.createPayment('stripe', { publishableKey: payInfo.value.publishable_key, ...payInfo.value });

        ElMessage.info('正在初始化Stripe支付...');

        const result = await stripe.createPayment(amount, orderNumber);

        if (result && result.success) {
            // 调用后端API确认支付
            // await PayWithStripe(orderNumber, {
            //     paymentIntentId: result.paymentIntentId
            // });

            ElMessage.success('Stripe支付成功！');
            router.push('/order');
        }
    } catch (error) {
        console.error('Stripe支付失败:', error);
        ElMessage.error('Stripe支付失败');
        throw error;
    }
};

// BitPay支付处理
const handleBitPayPayment = async (orderNumber, amount) => {
    try {
        const bitpay = PaymentFactory.createPayment('bitpay', payInfo.value);

        const result = await bitpay.createPayment(amount, orderNumber);

        if (result && result.success) {
            // 调用后端API记录支付信息
            // await PayWithBitPay(orderNumber, {
            //     invoiceId: result.invoiceId,
            //     paymentUrl: result.paymentUrl
            // });

            ElMessage.success('BitPay支付链接已打开，请在新窗口中完成支付');

            // 开始轮询支付状态
            pollPaymentStatus(orderNumber, 'bitpay');
        }
    } catch (error) {
        console.error('BitPay支付失败:', error);
        ElMessage.error('BitPay支付失败');
        throw error;
    }
};

// 微信支付处理
const handleWePayPayment = async (orderNumber, amount) => {
    try {
        const wepay = PaymentFactory.createPayment('wepay', payInfo.value);

        const result = await wepay.createPayment(amount, orderNumber);

        if (result && result.success) {
            // 调用后端API记录支付信息
            // await PayWithWePay(orderNumber, {
            //     prepayId: result.prepayId,
            //     codeUrl: result.codeUrl
            // });

            ElMessage.success('微信支付二维码已生成，请扫码支付');

            // 开始轮询支付状态
            pollPaymentStatus(orderNumber, 'wepay');
        }
    } catch (error) {
        console.error('微信支付失败:', error);
        ElMessage.error('微信支付失败');
        throw error;
    }
};

// 轮询支付状态
const pollPaymentStatus = async (orderNumber, paymentMethod, maxAttempts = 60) => {
    let attempts = 0;

    // const poll = async () => {
    //     try {
    //         attempts++;
    //         const status = await GetPaymentStatus(orderNumber, paymentMethod);

    //         if (status.paid) {
    //             ElMessage.success('支付成功！');
    //             router.push('/order');
    //             return;
    //         }

    //         if (status.failed) {
    //             ElMessage.error('支付失败');
    //             return;
    //         }

    //         if (attempts < maxAttempts) {
    //             setTimeout(poll, 3000); // 每3秒检查一次
    //         } else {
    //             ElMessage.warning('支付状态检查超时，请手动刷新页面查看订单状态');
    //         }
    //     } catch (error) {
    //         console.error('检查支付状态失败:', error);
    //         if (attempts < maxAttempts) {
    //             setTimeout(poll, 3000);
    //         }
    //     }
    // };

    // poll();
};

onMounted(() => {
    if (step.value === 1) {
        loadCartList();
    } else if (step.value === 2) {
        loadMiniCart();
        loadAddressList();
    }

    GetCheckoutInfo().then(res => {
        console.log('结算页信息:', res);
        // paymentMethods.value = res.payment_methods?.map(method => ({
        //     id: method.code,
        //     ...method,
        // }));
    })

});
</script>
<style lang="scss" scoped>
@import url('./index.scss');

.loading-state,
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #666;
    font-size: 1.4rem;
}

.cart_view_box_btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* 地址相关样式 */
.cart_view_box_address {
    position: relative;
    border: 1px solid #e0e0e0;
    border-radius: 0.8rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
        border-color: #6e4aeb;
        box-shadow: 0 2px 8px rgba(110, 74, 235, 0.1);
    }

    &.selected {
        border-color: #6e4aeb;
        background: rgba(110, 74, 235, 0.05);
    }

    .cart_view_box_address_user {
        font-size: 1.4rem;
        font-weight: 600;
        color: #303133;
        margin-bottom: 0.5rem;

        .default-tag {
            display: inline-block;
            padding: 0.2rem 0.6rem;
            background: #6e4aeb;
            color: white;
            font-size: 1.2rem;
            border-radius: 0.3rem;
            margin-left: 1rem;
        }
    }

    .cart_view_box_address_detail {
        font-size: 1.3rem;
        color: #666;
        line-height: 1.4;
    }

    .cart_view_box_address_action {
        display: flex;
        gap: 0.8rem;

        .cart_view_box_address_action_icon {
            width: 1.8rem;
            height: 1.8rem;
            cursor: pointer;
            opacity: 0.6;
            transition: opacity 0.2s;

            &:hover {
                opacity: 1;
            }
        }
    }
}

.cart_view_box_add {
    border: 1px dashed #d0d0d0;
    border-radius: 0.8rem;
    padding: 0 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s;
    color: #666;
    font-size: 1.4rem;

    &:hover {
        border-color: #6e4aeb;
        color: #6e4aeb;
    }

    .cart_view_box_add_img {
        width: 2rem;
        height: 2rem;
        margin-right: 0.8rem;
        vertical-align: middle;
    }
}

.empty-address {
    text-align: center;
    padding: 3rem;
    color: #999;
    font-size: 1.4rem;

    p {
        margin: 0;
    }
}

/* 支付方式样式 */
.cart_view_box_payment {
    display: flex;
    gap: 1.5rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;

    .cart_view_box_payment_item {
        position: relative;
        border: 2px solid #e0e0e0;
        border-radius: 0.8rem;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 8rem;
        height: 5rem;

        &:hover {
            border-color: #6e4aeb;
            box-shadow: 0 2px 8px rgba(110, 74, 235, 0.1);
            transform: translateY(-2px);
        }

        &.selected {
            border-color: #6e4aeb;
            background: rgba(110, 74, 235, 0.05);
            box-shadow: 0 4px 12px rgba(110, 74, 235, 0.2);

            &::after {
                content: '✓';
                position: absolute;
                top: -0.5rem;
                right: -0.5rem;
                width: 2rem;
                height: 2rem;
                background: #6e4aeb;
                color: white;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                font-weight: bold;
            }
        }

        .cart_view_box_payment_item_icon {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }
    }
}
</style>
import { ElMessage, ElLoading } from 'element-plus';
import { CatchreatePayPalPayment, CapturePayPalPayment } from '@api/payment';

/**
 * PayPal支付处理
 */
export class PayPalPayment {
  constructor(config = {}) {
    this.clientId = config.clientId;
    this.environment = config.environment || 'sandbox'; // sandbox 或 production
    this.currency = config.currency || 'USD';
  }

  // 初始化PayPal SDK
  async initSDK() {
    return new Promise((resolve, reject) => {
      if (window.paypal) {
        resolve(window.paypal);
        return;
      }

      const script = document.createElement('script');
      script.src = `https://www.paypal.com/sdk/js?client-id=${this.clientId}&currency=${this.currency}`;
      script.onload = () => resolve(window.paypal);
      script.onerror = () => reject(new Error('PayPal SDK加载失败'));
      document.head.appendChild(script);
    });
  }

  // 创建支付
  async createPayment(
    amount,
    orderNumber,
    containerId = 'paypal-button-container'
  ) {
    try {
      console.log('创建支付', amount, orderNumber);
      const paypal = await this.initSDK();

      return paypal
        .Buttons({
          createOrder: (data, actions) => {
            CatchreatePayPalPayment({ orderNumber });
            return actions.order.create({
              purchase_units: [
                {
                  amount: {
                    value: amount,
                    currency_code: this.currency,
                  },
                  custom_id: orderNumber,
                },
              ],
            });
          },
          onApprove: async (data, actions) => {
            CapturePayPalPayment({
              orderNumber,
              paypalOrderId: data.orderID,
            });
            const order = await actions.order.capture();
            return {
              success: true,
              orderId: data.orderID,
              paymentId: order.id,
              details: order,
            };
          },
          onError: (err) => {
            console.error('PayPal支付错误:', err);
            ElMessage.error('PayPal支付失败');
            return { success: false, error: err };
          },
        })
        .render(`#${containerId}`);
    } catch (error) {
      console.error('PayPal初始化失败:', error);
      ElMessage.error('PayPal初始化失败');
      throw error;
    }
  }
}

/**
 * Stripe支付处理
 */
export class StripePayment {
  constructor(config = {}) {
    console.log('StripePayment config:', config);
    this.publishableKey = config.publishableKey;
    this.client_secret = config.secret_key;
    this.stripe = null;
  }

  // 初始化Stripe SDK
  async initSDK() {
    return new Promise((resolve, reject) => {
      if (window.Stripe) {
        this.stripe = window.Stripe(this.publishableKey);
        resolve(this.stripe);
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://js.stripe.com/v3/';
      script.onload = () => {
        this.stripe = window.Stripe(this.publishableKey);
        resolve(this.stripe);
      };
      script.onerror = () => reject(new Error('Stripe SDK加载失败'));
      document.head.appendChild(script);
    });
  }

  // 创建支付
  async createPayment(amount, orderNumber, currency = 'usd') {
    try {
      await this.initSDK();

      const { error, paymentIntent } = await this.stripe.confirmCardPayment(
        this.client_secret
      );

      if (error) {
        console.error('Stripe支付错误:', error);
        ElMessage.error('Stripe支付失败: ' + error.message);
        return { success: false, error };
      }

      return {
        success: true,
        paymentIntentId: paymentIntent.id,
        details: paymentIntent,
      };
    } catch (error) {
      console.error('Stripe支付处理失败:', error);
      ElMessage.error('Stripe支付处理失败');
      throw error;
    }
  }
}

/**
 * BitPay支付处理
 */
export class BitPayPayment {
  constructor(config = {}) {
    this.apiUrl = config.apiUrl || 'https://bitpay.com/api';
    this.environment = config.environment || 'test'; // test 或 prod
  }

  // 创建支付
  async createPayment(amount, orderNumber, currency = 'USD') {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在创建BitPay支付...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 调用后端API创建BitPay发票
      const response = await fetch('/api/bitpay/create-invoice', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          price: amount,
          currency,
          orderNumber,
          redirectURL: window.location.origin + '/payment/success',
          notificationURL: window.location.origin + '/api/bitpay/webhook',
        }),
      });

      const invoice = await response.json();
      loading.close();

      if (invoice.url) {
        // 跳转到BitPay支付页面
        window.open(invoice.url, '_blank');
        return {
          success: true,
          invoiceId: invoice.id,
          paymentUrl: invoice.url,
          details: invoice,
        };
      } else {
        throw new Error('创建BitPay发票失败');
      }
    } catch (error) {
      console.error('BitPay支付创建失败:', error);
      ElMessage.error('BitPay支付创建失败');
      throw error;
    }
  }
}

/**
 * 微信支付处理
 */
export class WePayPayment {
  constructor(config = {}) {
    this.appId = config.appId;
    this.environment = config.environment || 'sandbox';
  }

  // 创建支付
  async createPayment(amount, orderNumber) {
    try {
      const loading = ElLoading.service({
        lock: true,
        text: '正在创建微信支付...',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 调用后端API创建微信支付订单
      const response = await fetch('/api/wepay/create-order', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // 微信支付使用分为单位
          orderNumber,
          description: `订单支付 - ${orderNumber}`,
        }),
      });

      const paymentData = await response.json();
      loading.close();

      if (paymentData.code_url) {
        // 显示二维码供用户扫描
        this.showQRCode(paymentData.code_url, orderNumber);
        return {
          success: true,
          prepayId: paymentData.prepay_id,
          codeUrl: paymentData.code_url,
          details: paymentData,
        };
      } else {
        throw new Error('创建微信支付订单失败');
      }
    } catch (error) {
      console.error('微信支付创建失败:', error);
      ElMessage.error('微信支付创建失败');
      throw error;
    }
  }

  // 显示二维码
  showQRCode(codeUrl, orderNumber) {
    // 这里可以使用qrcode库生成二维码
    // 或者直接显示二维码URL让用户扫描
    const qrWindow = window.open('', '_blank', 'width=400,height=500');
    qrWindow.document.write(`
      <html>
        <head><title>微信支付</title></head>
        <body style="text-align: center; padding: 20px;">
          <h3>请使用微信扫描二维码支付</h3>
          <div id="qrcode"></div>
          <p>订单号: ${orderNumber}</p>
          <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
          <script>
            QRCode.toCanvas(document.getElementById('qrcode'), '${codeUrl}', function (error) {
              if (error) console.error(error);
            });
          </script>
        </body>
      </html>
    `);
  }
}

/**
 * 支付工厂类
 */
export class PaymentFactory {
  static createPayment(method, config = {}) {
    switch (method) {
      case 'paypal':
        return new PayPalPayment(config);
      case 'stripe':
        return new StripePayment(config);
      case 'bitpay':
        return new BitPayPayment(config);
      case 'wepay':
        return new WePayPayment(config);
      default:
        throw new Error(`不支持的支付方式: ${method}`);
    }
  }
}

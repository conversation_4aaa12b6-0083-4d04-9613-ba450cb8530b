.header {
  background: #ffffff;
  width: 100%;
  height: 8rem;
  color: #242426;
  box-sizing: border-box;
  padding: 0 9.7rem;
  position: sticky;
  top: 0;
  z-index: 99;
  &_view {
    height: 8.5rem;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #ffffff;
    &_logo {
      height: 3.1rem;
      width: auto;
    }
    &_nav {
      display: flex;
      align-items: center;
      gap: 1.5rem;
      &_item {
        cursor: pointer;
        font-size: 1.6rem;
        color: #242426;
      }
    }
    &_action {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      gap: 2.9rem;
      &_item {
        width: 2.3rem;
        height: 2.3rem;
        cursor: pointer;
        position: relative;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.4rem;
        color: #242426;
        display: flex;
        align-items: center;
        cursor: pointer;
        &_arrow {
        }
        &_icon {
          width: 2.3rem;
          height: 2.3rem;
        }
        &_menu {
          display: none;
          flex-direction: column;
          padding: 0.8rem 0;
          width: 8.7rem;
          position: absolute;
          top: 2.3rem;
          left: -2rem;
          background: #ffffff;
          box-shadow: 0px 0.4rem 1rem 0px rgba(0, 0, 0, 0.1);
          border-radius: 0.8rem;
          &_item {
            margin-bottom: 0.4rem;
            height: 2rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 400;
            font-size: 1.4rem;
            color: #242426;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            cursor: pointer;
            &_icon {
              width: 1.6rem;
              height: 1.6rem;
            }
          }
          &_item:hover {
            background: #f3f4f8;
          }
        }
      }
      .unit {
        margin-right: 2rem;
      }
      .lang:hover {
        .header_view_action_item_arrow {
          transform: rotate(180deg);
        }
        .header_view_action_item_menu {
          display: flex;
        }
      }
      .unit:hover {
        .header_view_action_item_arrow {
          transform: rotate(180deg);
        }
        .header_view_action_item_menu {
          display: flex;
        }
      }
      &_line {
        width: 2.3rem;
        height: 0rem;
        border: 0.1rem solid #d5d5d5;
        transform: rotate(90deg);
      }
    }
  }
}
.footer {
  background-color: #202020;
  padding: 9.7rem 30.1rem;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &_main {
    &_title {
      height: 5.4rem;
      line-height: 5.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #ffffff;
      margin-bottom: 1.2rem;
    }
    &_tip {
      height: 2.7rem;
      line-height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;
    }
    &_link {
      height: 2.7rem;
      line-height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;
      margin-bottom: 6.5rem;
      span {
        text-decoration: underline;
      }
    }
    &_concat {
      display: flex;
      align-items: center;
      &_f {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 3.9rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        margin-right: 2.3rem;
        width: 5rem;
        height: 5rem;
        border-radius: 100%;
        &_icon {
          width: 2.3rem;
          height: 2.3rem;
        }
      }
      &_item {
        cursor: pointer;
        margin-right: 2.3rem;
        width: 5rem;
        height: 5rem;
      }
    }
  }
  &_info {
    width: 654px;
    &_title {
      height: 5.4rem;
      line-height: 5.4rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 3.9rem;
      color: #ffffff;
      margin-bottom: 12px;
    }
    &_desc {
      height: 2.7rem;
      line-height: 2.7rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.9rem;
      color: #ffffff;
      margin-bottom: 3.3rem;
    }
    &_action {
      display: flex;
      align-items: center;
      height: 5.8rem;
      margin-bottom: 3.3rem;
      &_input {
        width: 45.2rem;
        height: 5.8rem;
        border-radius: 3.1rem;
        border: 0.1rem solid #ffffff;
        padding: 0 2.7rem;
        box-sizing: border-box;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 1.9rem;
        color: #ffffff;
      }
      &_btn {
        width: 178px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 2.9rem;
        height: 5.8rem;
        background: #ffffff;
        border-radius: 3.1rem;
        font-family: PingFang SC, PingFang SC;
        font-weight: 600;
        font-size: 1.9rem;
        color: #000000;
        &_icon {
          margin-left: 12px;
          width: 2.3rem;
          height: 2.3rem;
        }
      }
    }
    &_tip {
      height: 72px;
      line-height: 36px;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.4rem;
      color: #ffffff;
    }
  }
}
.isEmpty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding-top: 20%;
  &_action {
    width: 8rem;
    background: linear-gradient(90deg, rgb(255, 119, 0), rgb(255, 73, 0));
    vertical-align: top;
    border-radius: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-family: PingFang SC;
    height: 3.6rem;
    line-height: 2.2rem;
    outline: 0;
    text-align: center;
    font-size: 1.4rem;
    color: #ffffff;
    font-weight: bold;
    cursor: pointer;
  }
}
.drawer_header {
  height: 8.5rem;
  position: relative;
  padding: 0 2.3rem;
  box-sizing: border-box;
  &_main {
    height: 8.5rem;
    display: flex;
    align-items: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 1.9rem;
    color: #242426;
    border-bottom: 0.1rem solid #e7e7e7;
  }
  &_icon {
    position: absolute;
    right: 2.3rem;
    top: 50%;
    transform: translateY(-50%);
    width: 2.3rem;
    height: 2.3rem;
    cursor: pointer;
  }
}
.cart_view {
  &_item {
    display: flex;
    align-items: center;
    padding: 3rem 0;
    position: relative;
    border-bottom: 0.1rem solid #e7e7e7;
    &_checkbox {
      margin-right: 10px;
      color: rgb(255, 119, 0);
    }
    &_img {
      width: 10rem;
      height: 10rem;
      margin-right: 10px;
    }
    &_main {
      padding-left: 2rem;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
    }
    &_name {
      height: 2.2rem;
      line-height: 2.2rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 1.6rem;
      color: #000000;
      margin-bottom: 1.2rem;
    }
    &_price {
      height: 2.8rem;
      line-height: 2.8rem;
      font-family: PingFang SC, PingFang SC;
      font-weight: 600;
      font-size: 2rem;
      color: #000000;
      margin-bottom: 1.4rem;
    }
    &_del {
      position: absolute;
      cursor: pointer;
      top: 50%;
      transform: translateY(-50%);
      right: 0;
      width: 2.4rem;
      height: 2.4rem;
    }
  }
  &_footer {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    padding: 0 20px;
    &_submit {
      margin: 2.4rem auto;
      width: 100%;
      height: 4.8rem;
      background: #6e4aeb;
      border-radius: 3.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #ffffff;
      font-weight: 600;
      font-size: 1.8rem;
      cursor: pointer;
    }
    &_cart {
      width: 100%;
      height: 4.8rem;
      background: #f6f6f6;
      border-radius: 3.2rem;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #242426;
      border: 0.1rem solid #e7e7e7;
      box-sizing: border-box;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 1.8rem;
      cursor: pointer;
    }
  }
}

<template>
    <div class="product_list">
        <div v-for="item in products" :key="item.id" class="product_list_item" @click="onShowDetail(item.id)">
            <img :src="item.images && item.images.length > 0 ? item.images[0] : ''" class="product_list_item_img"
                :alt="item.name" />
            <h2 class="product_list_item_name">{{ item.name }}</h2>
            <div class="product_list_item_info">
                <div class="product_list_item_info_price">{{ item.price_format }}</div>
                <div class="product_list_item_info_sale">
                    {{ t('home.sale') }}: {{ item.sales }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const { t } = useI18n();
const router = useRouter();

// 定义props接收商品数据
const props = defineProps({
    products: {
        type: Array,
        default: () => []
    }
});

const onShowDetail = (id) => {
    router.push(`/goods/${id}`)
}
</script>
<style lang="scss" scoped>
.product_list {
    display: flex;
    gap: 2.6rem;

    &_item {
        width: 31rem;
        height: 43.6rem;
        background: #FFFFFF;
        border-radius: 1.9rem;
        padding: 2.7rem 2.9rem;
        box-sizing: border-box;
        cursor: pointer;

        &_img {
            width: 25.2rem;
            height: 25.2rem;
            margin-bottom: 3.1rem;
            object-fit: cover;
            border-radius: 1rem;
        }

        &_name {
            height: 3.3rem;
            line-height: 3.3rem;
            font-family: PingFang SC, PingFang SC;
            font-weight: 600;
            font-size: 2.3rem;
            color: #242426;
            margin-bottom: 2.9rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        &_info {
            display: flex;
            align-items: center;
            justify-content: space-between;

            &_price {
                padding: 0 0.8rem;
                height: 35px;
                background: #F3EFFE;
                border-radius: 0.4rem;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: PingFang SC, PingFang SC;
                font-weight: 600;
                font-size: 1.9rem;
                color: #6E4AEB;
            }

            &_sale {
                font-family: PingFang SC, PingFang SC;
                font-weight: 400;
                font-size: 1.6rem;
                color: #70707B;
            }
        }
    }
}
</style>
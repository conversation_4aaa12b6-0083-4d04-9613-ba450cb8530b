<template>
  <header class="header">
    <div class="header_view">
      <img src="@assets/logo.png" class="header_view_logo" />
      <div class="header_view_nav">
        <div class="header_view_nav_item" @click="onGoUrl('/')">{{ t('app.home') }}</div>
        <div class="header_view_nav_item" @click="onGoUrl('/download')">{{ t('app.app') }}</div>
        <div class="header_view_nav_item" @click="onGoUrl('/goods')">{{ t('app.product') }}</div>
        <div class="header_view_nav_item" @click="onGoUrl('/wish')">{{ t('app.like') }}</div>
      </div>
      <div class="header_view_action">
        <div class="header_view_action_item">
          <img src="@assets/search.png" class="header_view_action_item_icon" />
        </div>
        <div class="header_view_action_item">
          <img src="@assets/user.png" class="header_view_action_item_icon" @click="onGoUrl('/user')" />
        </div>
        <img src="@assets/cart.png" class="header_view_action_item" @click="drawer = true" />
        <div class="header_view_action_line"></div>
        <div class="header_view_action_item unit">
          {{ unit }}&nbsp;
          <div class="header_view_action_item_arrow">
            <el-icon>
              <ArrowDown />
            </el-icon>
          </div>
          <div class="header_view_action_item_menu">
            <div v-for="item in unitList" :key="item.id" class="header_view_action_item_menu_item"
              @click="onChange(item.name)">
              <img :src="item.icon" class="header_view_action_item_menu_item_icon" />
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="header_view_action_item lang">
          {{ locale }}&nbsp;
          <div class="header_view_action_item_arrow">
            <el-icon>
              <ArrowDown />
            </el-icon>
          </div>
          <div class="header_view_action_item_menu">
            <div v-for="item in langList" :key="item.id" class="header_view_action_item_menu_item"
              @click="onChangeLang(item.lang)">
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </header>
  <router-view></router-view>
  <footer class="footer">
    <div class="footer_main">
      <h4 class="footer_main_title">联系我们</h4>
      <div class="footer_main_tip">可以在我们的博客查看公告</div>
      <div class="footer_main_link">
        媒体联系：<span><EMAIL></span>
      </div>
      <div class="footer_main_concat">
        <div class="footer_main_concat_f">
          <img src="@assets/f.png" class="footer_main_concat_f_icon" />
        </div>
        <img src="@assets/x.png" class="footer_main_concat_item" />
        <img src="@assets/i.png" class="footer_main_concat_item" />
        <img src="@assets/youtube.png" class="footer_main_concat_item" />
        <img src="@assets/douyin.png" class="footer_main_concat_item" />
      </div>
    </div>
    <div class="footer_info">
      <div class="footer_info_title">订阅我们的时事通讯</div>
      <div class="footer_info_desc">支持新币种，博客更新和独家优惠直接发送到您的收件箱</div>
      <div class="footer_info_action">
        <input class="footer_info_action_input" placeholder="请输入您的电子邮箱" />
        <div class="footer_info_action_btn">
          订阅新闻
          <img src="@assets/arrow.png" class="footer_info_action_btn_icon" />
        </div>
      </div>
      <div class="footer_info_tip">
        您的电子邮件地址仅用于向您发送我们的新闻通讯、最新资讯和优惠信息。您可以随时通过新闻通讯中的链接取消订阅。 了解更多关于我们如何管理您的数据和您的权利。
      </div>
    </div>
  </footer>
  <el-drawer v-model="drawer" :show-close="false" title="" :direction="direction" width="465px">
    <template #header>
      <div class="drawer_header">
        <div class="drawer_header_main">
          购物车
          <span></span>
          <img class="drawer_header_icon" src="@assets/close.png" @click="drawer = false" />
        </div>
      </div>
    </template>
    <div class="isEmpty" v-if="isEmpty">
      <el-icon style="cursor: pointer; font-size: 24px;">
        <ShoppingCart />
      </el-icon>
      <div class="isEmpty_action" @click="drawer = false; onGoUrl('/goods')">去购物</div>
    </div>
    <div class="cart_view" v-else>
      <div v-if="loading" class="loading-state">
        加载中...
      </div>
      <div v-else>
        <div class="cart_view_item" v-for="item in cartList" :key="item.cart_id">
          <el-checkbox :model-value="item.selected" @change="(checked) => handleSelectItem(item.cart_id, checked)"
            class="cart_view_item_checkbox"></el-checkbox>
          <img :src="item.image_url" class="cart_view_item_img" />
          <div class="cart_view_item_main">
            <p class="cart_view_item_name">{{ item.name_format }}</p>
            <span class="cart_view_item_price">{{ item.price_format }}</span>
            <el-input-number :model-value="item.quantity" :min="1" :max="item.stock"
              @change="(value) => handleUpdateQuantity(item.cart_id, value)" :step="1" size="small" />
            <img src="@assets/trash.png" class="cart_view_item_del" @click="handleRemoveItem(item.cart_id)" />
          </div>
        </div>
      </div>
    </div>
    <template #footer v-if="!isEmpty">
      <div class="cart_view_footer">
        <el-checkbox :model-value="selectAll" @change="handleSelectAll" class="cart_view_footer_checkbox">全选 ({{
          selectedCount }})</el-checkbox>
        <div class="cart_view_footer_total">{{ totalAmount }}</div>
        <div class="cart_view_footer_submit" @click="handleCheckout">{{ t('cart.toPayment') }}</div>
        <div class="cart_view_footer_cart" @click="handleViewCart">{{ t('cart.action') }}</div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n';
import { ref, onMounted, computed, watch } from "vue";
import { ElMessage } from 'element-plus';
import {
  GetCartList,
  SelectCartItems,
  UnselectCartItems,
  UpdateCart,
  RemoveCartItem
} from '@api/cart';
const { t, locale } = useI18n();
import RMB from '@assets/RMB.png';
import USD from '@assets/USD.png';
import EUR from '@assets/EUR.png';

const router = useRouter();
const unit = ref('USD');
const unitImg = ref({
  'USD': '$',
  '人民币': '￥',
  '欧元': '€'
});

const unitList = ref([
  {
    id: 0,
    name: 'USD',
    icon: USD
  },
  {
    id: 1,
    name: 'RMB',
    icon: RMB
  },
  {
    id: 2,
    name: 'EUR',
    icon: EUR
  }
]);

const langList = ref([
  {
    id: 0,
    name: 'Espanol',
    lang: 'Espanol'
  },
  {
    id: 1,
    name: 'French',
    lang: 'fr'
  },
  {
    id: 2,
    name: 'Indonesia',
    lang: 'Indonesia'
  },
  {
    id: 3,
    name: 'Italiano',
    lang: 'Italiano'
  },
  {
    id: 4,
    name: 'Japanese',
    lang: 'Japanese'
  },
  {
    id: 5,
    name: '간체 중문',
    lang: 'kr'
  },
  {
    id: 6,
    name: 'Русский',
    lang: '​Ру'
  },
  {
    id: 7,
    name: '繁体中文',
    lang: 'zhCN'
  },
  {
    id: 8,
    name: 'Deutsch',
    lang: 'Deutsch'
  },
  {
    id: 9,
    name: '中文',
    lang: 'zh'
  },
  {
    id: 10,
    name: 'English',
    lang: 'en'
  }
])

const onChangeLang = (lang) => {
  locale.value = lang;
}

const keyword = ref('');
const drawer = ref(false);
const direction = ref('rtl');

// 购物车相关数据
const cartList = ref([]);
const loading = ref(false);
const selectAll = ref(false);

// 计算属性
const isEmpty = computed(() => cartList.value.length === 0);

const selectedItems = computed(() => {
  return cartList.value.filter(item => item.selected);
});

const selectedCount = computed(() => {
  return selectedItems.value.length;
});

const totalAmount = computed(() => {
  const total = selectedItems.value.reduce((sum, item) => {
    return sum + (item.price * item.quantity);
  }, 0);
  return `$${total.toFixed(2)}`;
});

// 监听选中状态变化，更新全选状态
watch(cartList, () => {
  selectAll.value = cartList.value.length > 0 && cartList.value.every(item => item.selected);
}, { deep: true });

// 监听抽屉打开，加载购物车数据
watch(drawer, (isOpen) => {
  if (isOpen) {
    loadCartList();
  }
});

// 获取购物车列表
const loadCartList = async () => {
  try {
    loading.value = true;
    const response = await GetCartList();

    if (response && response.carts) {
      cartList.value = response.carts.map(item => ({
        cart_id: item.cart_id,
        name_format: item.name_format,
        image_url: item.image_url,
        price: item.price,
        price_format: item.price_format,
        quantity: item.quantity,
        stock: item.stock,
        selected: item.selected || false,
        sku_id: item.sku_id
      }));
    }
  } catch (error) {
    console.error('获取购物车列表失败:', error);
    ElMessage.error('获取购物车列表失败');
  } finally {
    loading.value = false;
  }
};

// 全选/取消全选
const handleSelectAll = async (checked) => {
  try {
    const cartIds = cartList.value.map(item => item.cart_id);

    if (checked) {
      await SelectCartItems(cartIds);
      cartList.value.forEach(item => item.selected = true);
    } else {
      await UnselectCartItems(cartIds);
      cartList.value.forEach(item => item.selected = false);
    }
  } catch (error) {
    console.error('批量选择失败:', error);
    ElMessage.error('操作失败');
    selectAll.value = !checked;
  }
};

// 选中/取消选中单个商品
const handleSelectItem = async (cartId, checked) => {
  try {
    if (checked) {
      await SelectCartItems([cartId]);
    } else {
      await UnselectCartItems([cartId]);
    }

    const item = cartList.value.find(item => item.cart_id === cartId);
    if (item) {
      item.selected = checked;
    }
  } catch (error) {
    console.error('选择商品失败:', error);
    ElMessage.error('操作失败');

    const item = cartList.value.find(item => item.cart_id === cartId);
    if (item) {
      item.selected = !checked;
    }
  }
};

// 更新商品数量
const handleUpdateQuantity = async (cartId, quantity) => {
  try {
    const item = cartList.value.find(item => item.cart_id === cartId);
    if (!item) return;

    await UpdateCart(cartId, {
      quantity: quantity,
      sku_id: item.sku_id
    });

    item.quantity = quantity;
    ElMessage.success('数量更新成功');
  } catch (error) {
    console.error('更新数量失败:', error);
    ElMessage.error('更新数量失败');
    loadCartList();
  }
};

// 删除商品
const handleRemoveItem = async (cartId) => {
  try {
    await RemoveCartItem(cartId);
    cartList.value = cartList.value.filter(item => item.cart_id !== cartId);
    ElMessage.success('商品已删除');
  } catch (error) {
    console.error('删除商品失败:', error);
    ElMessage.error('删除商品失败');
  }
};

// 查看购物车 - 跳转到第一步
const handleViewCart = () => {
  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 1 } });
};

// 去结算 - 跳转到第二步
const handleCheckout = () => {
  if (selectedCount.value === 0) {
    ElMessage.warning('请选择商品');
    return;
  }

  drawer.value = false;
  router.push({ path: '/confirm', query: { step: 2 } });
};

const search = () => {

}

const onChange = (name) => {
  unit.value = name;
}

const onGoUrl = (url) => {
  router.push(url)
}
</script>
<style>
.el-drawer__header {
  padding: 0;
  box-sizing: border-box;
}

.el-drawer__body {
  padding: 0 2.4rem;
}

.loading-state {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.4rem;
}

.cart_view_footer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.cart_view_footer_total {
  color: #e74c3c;
  font-weight: 600;
  font-size: 1.6rem;
}

.cart_view_item_del:hover {
  opacity: 0.7;
  cursor: pointer;
}
</style>
<style lang="scss" scoped>
@import url(./app.scss);
</style>
